#!/usr/bin/env python3
"""
诊断2020-07西北部水体不连续问题的脚本
分析缺失比例分布和推理瓦片选择情况
"""

import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
import matplotlib.patches as mpatches
from pathlib import Path
import argparse
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def mosaic_tiles(tile_data):
    """
    将4D瓦片数据 (nx, ny, tile_h, tile_w) 拼接成2D大图
    """
    nx, ny, tile_h, tile_w = tile_data.shape

    # 创建输出数组
    mosaic_h = nx * tile_h
    mosaic_w = ny * tile_w
    mosaic = np.full((mosaic_h, mosaic_w), np.nan, dtype=tile_data.dtype)

    # 拼接瓦片
    for i in range(nx):
        for j in range(ny):
            row_start = i * tile_h
            row_end = row_start + tile_h
            col_start = j * tile_w
            col_end = col_start + tile_w

            mosaic[row_start:row_end, col_start:col_end] = tile_data[i, j]

    return mosaic

def analyze_missing_patterns(nc_file: Path, time_str: str = "2020-07", missing_threshold: float = 0.0):
    """
    分析指定时间的缺失模式和推理瓦片选择
    
    Args:
        nc_file: NetCDF文件路径
        time_str: 时间字符串 (YYYY-MM)
        missing_threshold: 推理阈值
    """
    logger.info(f"Analyzing {nc_file} for time {time_str}")
    
    with xr.open_dataset(nc_file, decode_times=True) as ds:
        # 找到对应的时间索引
        time_coords = ds.time.values
        target_time_idx = None
        
        for i, t in enumerate(time_coords):
            # 转换numpy.datetime64到pandas.Timestamp以便访问年月
            import pandas as pd
            ts = pd.Timestamp(t)
            time_str_file = f"{ts.year:04d}-{ts.month:02d}"
            if time_str_file == time_str:
                target_time_idx = i
                break
        
        if target_time_idx is None:
            logger.error(f"Time {time_str} not found in file")
            return None
        
        logger.info(f"Found time index {target_time_idx} for {time_str}")
        
        # 获取数据
        missing_prop = ds.missing_proportion.isel(time=target_time_idx).values
        original_data = ds.data.isel(time=target_time_idx).values

        logger.info(f"Data shapes: missing_prop={missing_prop.shape}, original_data={original_data.shape}")

        # 处理不同维度的数据
        if len(missing_prop.shape) == 2:
            # 2D数据，直接使用
            pass
        else:
            logger.error(f"Unexpected missing_prop shape: {missing_prop.shape}")
            return None

        if len(original_data.shape) == 4:
            # 4D数据，需要拼接成大图
            logger.info("Detected 4D original data, mosaicking tiles...")
            original_data = mosaic_tiles(original_data)
        elif len(original_data.shape) == 2:
            # 2D数据，直接使用
            pass
        else:
            logger.error(f"Unexpected original_data shape: {original_data.shape}")
            return None
        
        # 检查是否有推理后的数据
        has_inpainted = False
        if nc_file.name.startswith('inpainted_'):
            has_inpainted = True
            logger.info("This is an inpainted file")
        
        # 分析推理瓦片选择
        inference_mask = missing_prop > missing_threshold
        total_tiles = np.prod(missing_prop.shape)
        selected_tiles = np.sum(inference_mask)
        
        logger.info(f"Total tiles: {total_tiles}")
        logger.info(f"Selected for inference (missing > {missing_threshold}): {selected_tiles}")
        logger.info(f"Selection ratio: {selected_tiles/total_tiles:.3f}")
        
        # 统计缺失比例分布
        valid_mask = ~np.isnan(missing_prop)
        valid_missing = missing_prop[valid_mask]
        
        logger.info(f"Missing proportion statistics:")
        logger.info(f"  Min: {np.min(valid_missing):.4f}")
        logger.info(f"  Max: {np.max(valid_missing):.4f}")
        logger.info(f"  Mean: {np.mean(valid_missing):.4f}")
        logger.info(f"  Median: {np.median(valid_missing):.4f}")
        
        # 分析西北部区域 (假设左上角为西北部)
        nw_region = slice(0, missing_prop.shape[0]//3), slice(0, missing_prop.shape[1]//3)
        nw_missing = missing_prop[nw_region]
        nw_inference = inference_mask[nw_region]
        
        nw_valid_mask = ~np.isnan(nw_missing)
        if np.any(nw_valid_mask):
            nw_valid_missing = nw_missing[nw_valid_mask]
            nw_selected = np.sum(nw_inference[nw_valid_mask])
            nw_total = np.sum(nw_valid_mask)
            
            logger.info(f"Northwest region analysis:")
            logger.info(f"  Total valid tiles: {nw_total}")
            logger.info(f"  Selected for inference: {nw_selected}")
            logger.info(f"  Selection ratio: {nw_selected/nw_total:.3f}")
            logger.info(f"  Missing prop range: {np.min(nw_valid_missing):.4f} - {np.max(nw_valid_missing):.4f}")
        
        return {
            'missing_prop': missing_prop,
            'inference_mask': inference_mask,
            'original_data': original_data,
            'time_idx': target_time_idx,
            'has_inpainted': has_inpainted,
            'nw_region': nw_region
        }

def visualize_analysis(analysis_results: dict, output_path: Path, title_suffix: str = ""):
    """可视化分析结果"""
    
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    
    # 1. 缺失比例分布
    ax = axes[0, 0]
    missing_prop = analysis_results['missing_prop']
    valid_mask = ~np.isnan(missing_prop)
    im1 = ax.imshow(missing_prop, cmap='YlOrRd', vmin=0, vmax=1)
    ax.set_title(f'Missing Proportion {title_suffix}')
    ax.axis('off')
    plt.colorbar(im1, ax=ax, fraction=0.046)
    
    # 2. 推理瓦片选择
    ax = axes[0, 1]
    inference_mask = analysis_results['inference_mask'].astype(float)
    inference_mask[~valid_mask] = np.nan
    im2 = ax.imshow(inference_mask, cmap='RdYlBu_r', vmin=0, vmax=1)
    ax.set_title(f'Inference Selection {title_suffix}')
    ax.axis('off')
    plt.colorbar(im2, ax=ax, fraction=0.046)
    
    # 3. 原始数据
    ax = axes[1, 0]
    original_data = analysis_results['original_data']
    # 假设JRC编码：0=no_obs, 1=land, 2=water, 255=no_data
    display_data = original_data.astype(float)
    display_data[original_data == 255] = np.nan
    display_data[original_data == 0] = np.nan
    display_data[original_data == 1] = 0  # land -> 0
    display_data[original_data == 2] = 1  # water -> 1
    
    im3 = ax.imshow(display_data, cmap='YlGnBu', vmin=0, vmax=1)
    ax.set_title(f'Water Data {title_suffix}')
    ax.axis('off')
    plt.colorbar(im3, ax=ax, fraction=0.046)
    
    # 4. 西北部区域高亮
    ax = axes[1, 1]
    highlight = np.zeros_like(missing_prop)
    nw_region = analysis_results['nw_region']
    highlight[nw_region] = 1
    highlight[~valid_mask] = np.nan
    
    im4 = ax.imshow(highlight, cmap='Reds', vmin=0, vmax=1, alpha=0.7)
    ax.imshow(missing_prop, cmap='gray', alpha=0.3)
    ax.set_title(f'Northwest Region Highlight {title_suffix}')
    ax.axis('off')
    
    plt.tight_layout()
    plt.savefig(output_path, dpi=150, bbox_inches='tight')
    plt.close()
    
    logger.info(f"Visualization saved to {output_path}")

def main():
    parser = argparse.ArgumentParser(description="诊断水体不连续问题")
    parser.add_argument('--nc_file', type=str, required=True, help='NetCDF文件路径')
    parser.add_argument('--time', type=str, default='2020-07', help='时间字符串 (YYYY-MM)')
    parser.add_argument('--missing_threshold', type=float, default=0.0, help='推理阈值')
    parser.add_argument('--output_dir', type=str, default='./prediction', help='输出目录')
    
    args = parser.parse_args()
    
    nc_file = Path(args.nc_file)
    output_dir = Path(args.output_dir)
    output_dir.mkdir(exist_ok=True)
    
    # 分析
    results = analyze_missing_patterns(nc_file, args.time, args.missing_threshold)
    
    if results is not None:
        # 可视化
        output_name = f"discontinuity_analysis_{nc_file.stem}_{args.time.replace('-', '_')}.png"
        output_path = output_dir / output_name
        
        title_suffix = f"({args.time}, threshold={args.missing_threshold})"
        visualize_analysis(results, output_path, title_suffix)
        
        logger.info("Analysis completed successfully")
    else:
        logger.error("Analysis failed")

if __name__ == "__main__":
    main()
