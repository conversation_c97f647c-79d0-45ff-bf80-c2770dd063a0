#!/bin/bash

# 诊断2020-07西北部水体不连续问题

echo "=== 诊断水体不连续问题 ==="

# 设置路径
JRC_FILE="/mnt/storage/xiaozhen/Water/Clip/JRC4/JRC_window_115.760_30.000.nc"
INPAINTED_FILE="/mnt/storage/xiaozhen/Water/Predictions/swin_waternet_v20_2/inpainted_JRC_window_115.760_30.000.nc"
PYTHON_PATH="/mnt/storage/xiaozhen/miniconda3/envs/geoai/bin/python"

echo "使用Python: $PYTHON_PATH"
echo "原始JRC文件: $JRC_FILE"
echo "推理后文件: $INPAINTED_FILE"

# 检查文件是否存在
if [ ! -f "$JRC_FILE" ]; then
    echo "错误: 原始JRC文件不存在: $JRC_FILE"
    exit 1
fi

if [ ! -f "$INPAINTED_FILE" ]; then
    echo "警告: 推理后文件不存在: $INPAINTED_FILE"
    echo "将只分析原始文件"
fi

echo ""
echo "=== 分析原始JRC文件 (2020-07) ==="
$PYTHON_PATH prediction/diagnose_discontinuity.py \
    --nc_file "$JRC_FILE" \
    --time "2020-07" \
    --missing_threshold 0.0 \
    --output_dir "./prediction"

echo ""
echo "=== 分析原始JRC文件 (2018-07) 作为对比 ==="
$PYTHON_PATH prediction/diagnose_discontinuity.py \
    --nc_file "$JRC_FILE" \
    --time "2018-07" \
    --missing_threshold 0.0 \
    --output_dir "./prediction"

if [ -f "$INPAINTED_FILE" ]; then
    echo ""
    echo "=== 分析推理后文件 (2020-07) ==="
    $PYTHON_PATH prediction/diagnose_discontinuity.py \
        --nc_file "$INPAINTED_FILE" \
        --time "2020-07" \
        --missing_threshold 0.0 \
        --output_dir "./prediction"
fi

echo ""
echo "=== 诊断完成 ==="
echo "请查看 prediction/ 目录下生成的分析图片"
echo ""
echo "建议的解决方案："
echo "1. 降低 missing_threshold 到负值（如 -0.1）以包含所有瓦片"
echo "2. 或者修改推理逻辑，对所有瓦片进行推理"
echo "3. 检查瓦片拼接策略，考虑使用平均值而非first策略"
