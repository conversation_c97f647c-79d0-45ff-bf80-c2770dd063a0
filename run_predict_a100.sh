source /mnt/storage/xiaozhen/miniconda3/bin/activate
conda activate geoai

python prediction/workflow.py \
     /mnt/storage/xiaozhen/Water/Clip/JRC4 \
    --config /home/<USER>/Water/configs/config_v20.yaml \
    --model /mnt/storage/xiaozhen/Water/Results/checkpoints/swin_waternet_v20_2/best.pt \
    --output /mnt/storage/xiaozhen/Water/Predictions/swin_waternet_v20_2 \
    --occurrence_raster /mnt/storage/xiaozhen/Water/Clip/occurrence \
    --region 115 28 118 30 \
    --device cuda:0 \
    --batch_size 32 \
    --num_workers 2 \
    --max_memory_gb 4 \
    --time_range 2020-09-01 2020-09-01 \